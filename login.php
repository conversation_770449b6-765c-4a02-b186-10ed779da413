<?php
require 'auth/guest_only.php';
require 'db.php';

$errors = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email    = $_POST['email'];
    $password = $_POST['password'];

    $akun = findUserByEmail($email);

    if ($akun && password_verify($password, $akun['password'])) {
        $_SESSION['user'] = [
            'id' => $akun['id'],
            'nama' => $akun['nama'],
            'email' => $akun['email'],
        ];
        header("Location: yyyy.php");
        exit;
    } else {
        $errors[] = "Email atau password salah";
    }
}
?>

<h2>Login</h2>
<form method="post">
    Email: <input name="email" type="email"><br>
    Password: <input name="password" type="password"><br>
    <button>Login</button>
</form>
<?php foreach ($errors as $e) echo "<p style='color:red'>$e</p>"; ?>

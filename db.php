<?php
// Simple file-based user storage for demo purposes
$users_file = 'users.json';

// Initialize users file if it doesn't exist
if (!file_exists($users_file)) {
    file_put_contents($users_file, json_encode([]));
}

function getUsers() {
    global $users_file;
    $content = file_get_contents($users_file);
    return json_decode($content, true) ?: [];
}

function saveUsers($users) {
    global $users_file;
    file_put_contents($users_file, json_encode($users, JSON_PRETTY_PRINT));
}

function findUserByEmail($email) {
    $users = getUsers();
    foreach ($users as $user) {
        if ($user['email'] === $email) {
            return $user;
        }
    }
    return null;
}

function addUser($nama, $email, $password) {
    $users = getUsers();

    // Check if email already exists
    if (findUserByEmail($email)) {
        return false;
    }

    $newUser = [
        'id' => count($users) + 1,
        'nama' => $nama,
        'email' => $email,
        'password' => password_hash($password, PASSWORD_BCRYPT)
    ];

    $users[] = $newUser;
    saveUsers($users);
    return $newUser;
}
?>

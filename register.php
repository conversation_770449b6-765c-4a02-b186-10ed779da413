<?php
require 'auth/guest_only.php';
require 'db.php';

$errors = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nama     = $_POST['nama'];
    $email    = $_POST['email'];
    $password = $_POST['password'];
    $confirm  = $_POST['confirm'];

    if ($password !== $confirm) {
        $errors[] = "Password tidak cocok";
    } else {
        $newUser = addUser($nama, $email, $password);
        if ($newUser) {
            $_SESSION['user'] = [
                'id' => $newUser['id'],
                'nama' => $newUser['nama'],
                'email' => $newUser['email'],
            ];
            header("Location: yyyy.php");
            exit;
        } else {
            $errors[] = "Email sudah digunakan";
        }
    }
}
?>

<h2>Register</h2>
<form method="post">
    Nama: <input name="nama"><br>
    Email: <input name="email" type="email"><br>
    Password: <input name="password" type="password"><br>
    Kon<PERSON>rma<PERSON>: <input name="confirm" type="password"><br>
    <button>Daftar</button>
</form>
<?php foreach ($errors as $e) echo "<p style='color:red'>$e</p>"; ?>
